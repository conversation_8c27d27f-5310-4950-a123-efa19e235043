import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  differenceInMinutes,
  min,
  subMinutes,
  isBefore,
  isAfter,
  startOfDay,
  endOfDay,
} from 'date-fns';
import { FilterQuery, Model } from 'mongoose';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
import { ShiftsService } from 'src/shifts/shifts.service';
import { ClockInInput, ClockOutInput } from './dto/clockin.input';
import { Attendance } from './entities/attendance.entity';
import { CreateAttendanceInput } from './dto/create-attendance.input';
import { Shift } from 'src/shifts/entities/shift.entity';
import { UpdateAttendanceInput } from './dto/update-attendance.input';
import { UsersService } from 'src/users/users.service';
import { AttendanceFilterInput } from './dto/attendance.input';
@Injectable()
export class AttendanceService {
  private readonly logger = new Logger(AttendanceService.name);

  constructor(
    @InjectModel(Attendance.name)
    private readonly attendance: Model<Attendance>,
    @InjectConnection()
    private readonly connection: Connection,
    private readonly shiftService: ShiftsService,
    private readonly usersService: UsersService,
  ) {}

  private timeAndOverTimeSpent({
    shift,
    attendanceStartTime,
  }: {
    shift: Shift;
    attendanceStartTime: Date;
  }) {
    const currentDateTime = new Date();

    // Calculate total time spent from attendance start time until either the current time or shift end (whichever is earlier)
    const timeSpentInMinutes = differenceInMinutes(
      min([currentDateTime, shift.endDateTime]),
      attendanceStartTime,
    );

    // Calculate overtime spent only if overtime exists and user has worked past endDateTime
    let overTimeSpentInMinutes = 0;
    if (shift.overTime) {
      overTimeSpentInMinutes = differenceInMinutes(
        min([currentDateTime, shift.overTime]),
        shift.endDateTime,
      );
    }

    return {
      timeSpentInMinutes,
      overTimeSpentInMinutes,
    };
  }

  private async verifyUserFace(userId: string, base64Img: string) {
    const user = await this.usersService.findOne({ _id: userId });
    if (!user?.faceInformation?.faceId) {
      throw new UnauthorizedException(
        'Face data not registered for attendance',
      );
    }

    const faceMatches = await this.usersService.searchFace(base64Img);
    const isValidFace = faceMatches?.FaceMatches?.some(
      (face) => face.Face?.ExternalImageId === userId,
    );

    if (!isValidFace) {
      throw new UnauthorizedException('Face verification failed');
    }
  }

  private async validateClockIn(
    userId: string,
    shiftId: string,
    date: Date,
    session?: any,
  ): Promise<void> {
    this.logger.debug(
      `Validating clock-in for user: ${userId}, shift: ${shiftId}, date: ${date.toISOString()}`,
    );

    // Get the shift details to check the time window
    const shift = await this.shiftService.findOne({ _id: shiftId });
    if (!shift) {
      throw new NotFoundException('Shift not found');
    }

    // Check if current time is within the allowed clock-in window
    const currentTime = new Date();
    const shiftStartWithBuffer = subMinutes(shift.startDateTime, 30); // 30 minutes buffer before shift start
    const shiftEnd = shift.endDateTime;

    // User can clock in from 30 minutes before shift start until shift end
    if (isBefore(currentTime, shiftStartWithBuffer)) {
      throw new ConflictException(
        'Cannot clock in more than 30 minutes before shift start time',
      );
    }

    if (isAfter(currentTime, shiftEnd)) {
      throw new ConflictException('Cannot clock in after shift end time');
    }

    // Check for existing attendance records more comprehensively
    // Use date range to handle potential time differences in date field
    const dateStart = startOfDay(date);
    const dateEnd = endOfDay(date);

    // First check: Look for any active attendance (no endTime) for this user and shift
    this.logger.debug(
      `Checking for active attendance for user: ${userId}, shift: ${shiftId}`,
    );
    const activeAttendance = await this.attendance
      .findOne({
        user: userId,
        shift: shiftId,
        endTime: null, // No end time means still clocked in
      })
      .session(session);

    if (activeAttendance) {
      this.logger.warn(
        `Found existing active attendance: ${activeAttendance._id} for user: ${userId}, shift: ${shiftId}`,
      );
      throw new ConflictException('User is already clocked in for this shift');
    }

    // Second check: Look for any attendance record for this user, shift, and date range
    this.logger.debug(
      `Checking for attendance in date range: ${dateStart.toISOString()} to ${dateEnd.toISOString()}`,
    );
    const existingAttendanceForDate = await this.attendance
      .findOne({
        user: userId,
        shift: shiftId,
        date: {
          $gte: dateStart,
          $lte: dateEnd,
        },
        endTime: null, // No end time means still clocked in
      })
      .session(session);

    if (existingAttendanceForDate) {
      this.logger.warn(
        `Found existing attendance for date: ${existingAttendanceForDate._id} for user: ${userId}, shift: ${shiftId}, date: ${existingAttendanceForDate.date}`,
      );
      throw new ConflictException(
        'User is already clocked in for this shift on this date',
      );
    }

    this.logger.debug(
      `Clock-in validation passed for user: ${userId}, shift: ${shiftId}`,
    );
  }

  private async validateClockOut(attendance: Attendance): Promise<void> {
    // Check if user has already clocked out
    if (attendance.endTime) {
      throw new ConflictException(
        'User has already clocked out for this shift',
      );
    }
  }

  async clockIn(
    userId: string,
    { date, locationId, shiftId, base64Img }: ClockInInput,
  ) {
    // Use a transaction to prevent race conditions
    const session = await this.connection.startSession();

    try {
      return await session.withTransaction(async () => {
        // Perform validation within the transaction
        await this.validateClockIn(userId, shiftId, date, session);
        await this.verifyUserFace(userId, base64Img);

        // Create the attendance record within the transaction
        const attendanceRecord = await this.attendance.create(
          [
            {
              location: locationId,
              shift: shiftId,
              user: userId,
              date,
              timeSpentInMinutes: 0,
              overTimeSpentInMinutes: 0,
              startTime: new Date(),
              endTime: null,
            },
          ],
          { session },
        );

        return attendanceRecord[0];
      });
    } finally {
      await session.endSession();
    }
  }

  async clockOut({ attendanceId, base64Img }: ClockOutInput) {
    const attendance = await this.attendance.findById(attendanceId);
    if (!attendance) throw new NotFoundException('Attendance not found');

    await this.validateClockOut(attendance);
    await this.verifyUserFace(attendance.user, base64Img);

    const shift = await this.shiftService.findOne({ _id: attendance.shift });
    if (!shift) throw new NotFoundException('Shift not found');

    const { timeSpentInMinutes, overTimeSpentInMinutes } =
      this.timeAndOverTimeSpent({
        shift,
        attendanceStartTime: attendance.startTime,
      });

    return this.attendance.findByIdAndUpdate(attendanceId, {
      endTime: new Date(),
      timeSpentInMinutes,
      overTimeSpentInMinutes,
    });
  }

  async createAttendance(attendance: CreateAttendanceInput) {
    const { date, locationId, shiftId, userId, startTime, endTime, overTime } =
      attendance;

    const shift = await this.shiftService.findOne({ _id: shiftId });
    if (!shift) throw new NotFoundException('Shift not found');

    const { timeSpentInMinutes, overTimeSpentInMinutes } =
      this.timeAndOverTimeSpent({
        shift,
        attendanceStartTime: startTime,
      });

    return this.attendance.create({
      location: locationId,
      shift: shiftId,
      user: userId,
      date,
      startTime,
      endTime,
      overTime,
      timeSpentInMinutes,
      overTimeSpentInMinutes,
    });
  }

  getAttendance(query: FilterQuery<Attendance> = {}) {
    return this.attendance.find(query);
  }

  findOne(filter: FilterQuery<Attendance>) {
    return this.attendance.findOne(filter);
  }

  async update(id: string, updateAttendanceInput: UpdateAttendanceInput) {
    const { id: _, ...updateData } = updateAttendanceInput;
    const { userId, date, startTime, endTime, overTime, locationId, shiftId } =
      updateData;
    return this.attendance.findByIdAndUpdate(
      id,
      {
        user: userId,
        date,
        startTime,
        endTime,
        overTime,
        location: locationId,
        shift: shiftId,
      },
      {
        new: true,
      },
    );
  }

  getAllAttendances(filter?: AttendanceFilterInput) {
    const query: FilterQuery<Attendance> = {};

    if (filter) {
      // Location filter
      if (filter.locationIds && filter.locationIds.length > 0) {
        query.location = { $in: filter.locationIds };
      }

      // Date range filter
      if (filter.dateRange) {
        const dateFilter: any = {};
        if (filter.dateRange.startDate) {
          dateFilter.$gte = startOfDay(filter.dateRange.startDate);
        }
        if (filter.dateRange.endDate) {
          dateFilter.$lte = endOfDay(filter.dateRange.endDate);
        }
        if (Object.keys(dateFilter).length > 0) {
          query.date = dateFilter;
        }
      }

      // User filter
      if (filter.userIds && filter.userIds.length > 0) {
        query.user = { $in: filter.userIds };
      }

      // Time spent filter (less than or equal to)
      if (filter.timeSpentLte !== undefined) {
        query.timeSpentInMinutes = { $lte: filter.timeSpentLte };
      }
    }

    return this.attendance.find(query);
  }
}
