import { Field, InputType, Int } from '@nestjs/graphql';
import { IsMongoId, IsOptional, IsDate, IsNumber, Min } from 'class-validator';

@InputType()
export class AttendanceInput {
  @Field(() => String)
  @IsMongoId()
  shiftId?: string;
}

@InputType()
export class DateRangeInput {
  @Field(() => Date, { nullable: true })
  @IsOptional()
  @IsDate()
  startDate?: Date;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  @IsDate()
  endDate?: Date;
}

@InputType()
export class AttendanceFilterInput {
  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsMongoId({ each: true })
  locationIds?: string[];

  @Field(() => DateRangeInput, { nullable: true })
  @IsOptional()
  dateRange?: DateRangeInput;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsMongoId({ each: true })
  userIds?: string[];

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  timeSpentLte?: number;
}
